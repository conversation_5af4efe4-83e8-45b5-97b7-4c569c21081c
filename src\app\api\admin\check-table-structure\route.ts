import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const { tableName, query, userId } = await request.json();

    // 如果提供了userId，查询用户信息
    if (userId) {
      const userQuery = `SELECT id, username, email, role, created_at FROM users WHERE id = ?`;
      const userResult = await executeQuery<any[]>(userQuery, [userId]);
      return NextResponse.json({
        success: true,
        data: userResult
      });
    }

    if (!tableName) {
      return NextResponse.json({
        success: false,
        error: '表名不能为空'
      }, { status: 400 });
    }

    // 如果有查询参数，执行查询
    if (query) {
      const queryResult = await executeQuery<any[]>(query, []);
      return NextResponse.json({
        success: true,
        tableName,
        data: queryResult
      });
    }

    // 检查表结构
    const tableStructure = await executeQuery<any[]>(
      `DESCRIBE ${tableName}`,
      []
    );

    return NextResponse.json({
      success: true,
      tableName,
      structure: tableStructure
    });

  } catch (error: any) {
    console.error('检查表结构失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message || '检查表结构失败'
    }, { status: 500 });
  }
}
