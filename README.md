# 微甜 AI Studio

一个基于 Next.js 15 的现代化 AI 对话与创作平台，支持智能体管理、用户权限控制、兑换码系统、AI提供商配置等企业级功能。

## ✨ 核心功能

### 🤖 智能对话系统
- 支持多种 AI 模型（OpenAI、<PERSON>、Gemini、DeepSeek）
- 智能体对话，支持 Coze 和内置智能体
- 会话管理和历史记录
- 实时流式对话响应

### 👥 智能体管理
- 创建和管理自定义 AI 智能体
- 支持智能体头像上传和管理
- 智能体分类和标签系统
- 预设提示词配置
- 使用次数统计和限制

### � 用户权限系统
- 基于角色的权限控制（admin/user）
- JWT 认证和会话管理
- 邮箱注册和验证
- 密码重置功能
- 用户设置和偏好管理

### 💳 订阅和付费系统
- 智能体订阅管理
- 兑换码生成和使用
- 试用次数控制
- 付费方案配置（按次/按时间）
- 使用统计和分析

### ⚙️ 系统配置
- AI 提供商配置管理
- 模型参数自定义
- 系统健康检查
- 数据库管理工具
- 批量数据处理

## 🛠️ 技术栈

### 前端技术
- **框架**: Next.js 15.1.6 (App Router)
- **UI库**: React 18.3.1, TypeScript 5.8.3
- **组件库**: Ant Design 5.26.6, Lobe UI 2.7.5
- **状态管理**: Zustand 5.0.4
- **样式**: Ant Design Style 3.7.1, Tailwind CSS 3.4.0
- **动画**: Framer Motion 12.23.6
- **图标**: Lucide React 0.525.0

### 后端技术
- **API**: Next.js API Routes
- **数据库**: MySQL 8.0 (mysql2 3.14.2)
- **认证**: JWT (jsonwebtoken 9.0.2), bcryptjs 3.0.2
- **邮件**: Nodemailer 7.0.5
- **文件处理**: 支持多种格式上传和处理

### 工具库
- **工具函数**: Lodash-es 4.17.21, Day.js 1.11.13
- **ID生成**: Nanoid 5.1.5
- **文档处理**: DOCX 9.5.1, XLSX 0.18.5
- **图像处理**: HTML2Canvas 1.4.1, Dom-to-image 2.6.0
- **加密**: js-sha256 0.11.1

### 部署方案
- **容器化**: Docker, Docker Compose
- **云部署**: Vercel (推荐)
- **数据库**: 支持云数据库（Aiven、PlanetScale等）

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- npm >= 8.0.0

### 本地开发

1. **克隆项目**
   ```bash
   git clone https://github.com/your-org/story-ai-studio.git
   cd story-ai-studio
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   # 复制环境配置文件
   cp .env.local.example .env.local
   # 编辑 .env.local 文件，配置数据库密码等信息
   ```

4. **初始化数据库**
   ```bash
   # 自动创建数据库和表结构
   npm run db:init
   ```

5. **启动开发服务器**
   ```bash
   npm run dev
   ```

6. **访问应用**
   打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### Docker 部署

1. **使用 Docker Compose（推荐）**
   ```bash
   # 复制环境配置文件
   cp .env.local.example .env.local
   # 编辑环境变量

   # 启动所有服务（包括MySQL和Redis）
   docker-compose up -d
   ```

2. **单独构建镜像**
   ```bash
   docker build -t story-ai-studio .
   docker run -p 3000:3000 story-ai-studio
   ```

3. **使用部署脚本**
   ```bash
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh production
   ```

## 📁 项目结构

```
微甜-ai-studio/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API 路由
│   │   │   ├── auth/          # 用户认证 (登录/注册/重置密码)
│   │   │   ├── agents/        # 智能体管理
│   │   │   ├── ai-providers/  # AI提供商配置
│   │   │   ├── ai-chat/       # AI对话接口
│   │   │   ├── agent-pricing/ # 智能体定价
│   │   │   ├── agent-remaining/ # 剩余次数查询
│   │   │   ├── redemption-codes/ # 兑换码系统
│   │   │   ├── user-settings/ # 用户设置
│   │   │   ├── admin/         # 管理员工具
│   │   │   └── health/        # 系统健康检查
│   │   ├── auth/              # 认证页面 (登录/注册)
│   │   ├── chat/              # 对话页面
│   │   ├── agents/            # 智能体管理页面
│   │   ├── settings/          # 系统设置页面
│   │   ├── redeem/            # 兑换码页面
│   │   └── help/              # 帮助页面
│   ├── components/            # React 组件
│   │   ├── agents/            # 智能体相关组件
│   │   ├── auth/              # 认证相关组件
│   │   ├── chat/              # 聊天界面组件
│   │   ├── settings/          # 设置页面组件
│   │   ├── redemption/        # 兑换码组件
│   │   ├── providers/         # AI提供商配置组件
│   │   └── common/            # 通用组件
│   ├── hooks/                 # React Hooks
│   │   ├── usePermissions.ts  # 权限管理
│   │   └── useSessionSearch.ts # 会话搜索
│   ├── store/                 # Zustand 状态管理
│   │   ├── auth.ts           # 用户认证状态
│   │   ├── chat.ts           # 聊天状态
│   │   ├── agent.ts          # 智能体状态
│   │   └── settings.ts       # 设置状态
│   ├── lib/                   # 核心库
│   │   ├── db.ts             # 数据库连接
│   │   ├── api.ts            # API 客户端
│   │   └── utils.ts          # 工具函数
│   ├── types/                 # TypeScript 类型定义
│   │   ├── agent.ts          # 智能体类型
│   │   ├── ai-provider.ts    # AI提供商类型
│   │   └── index.ts          # 通用类型
│   └── services/              # 业务服务
│       ├── aiService.ts      # AI服务
│       └── fileUpload.ts     # 文件上传
├── scripts/                   # 数据库脚本和部署工具
│   ├── init-db.sql           # 数据库初始化
│   ├── deploy-to-vercel.sh   # Vercel部署脚本
│   └── sync-to-aiven.js      # 数据库同步工具
├── public/                    # 静态资源
│   └── agents_shortcut/       # 智能体头像
├── mysql-config/              # MySQL 配置
├── docker-compose.yml         # Docker 编排
├── Dockerfile                 # Docker 镜像
├── vercel.json               # Vercel 配置
├── DEPLOYMENT.md             # 部署指南
└── package.json              # 项目配置
```

## 🔧 配置说明

### 环境变量配置

#### 🔧 必需配置

| 变量名 | 说明 | 示例值 | 必需 |
|--------|------|--------|------|
| `DB_HOST` | 数据库主机地址 | `localhost` 或云数据库地址 | ✅ |
| `DB_PORT` | 数据库端口 | `3306` | ✅ |
| `DB_USER` | 数据库用户名 | `root` | ✅ |
| `DB_PASSWORD` | 数据库密码 | `your_password` | ✅ |
| `DB_NAME` | 数据库名称 | `story_ai` | ✅ |
| `JWT_SECRET` | JWT 签名密钥 | `your_jwt_secret_key` | ✅ |

#### 📧 邮件配置（可选）

| 变量名 | 说明 | 示例值 | 必需 |
|--------|------|--------|------|
| `SMTP_HOST` | 邮件服务器主机 | `smtp.gmail.com` | ❌ |
| `SMTP_PORT` | 邮件服务器端口 | `587` | ❌ |
| `SMTP_USER` | 邮件服务器用户名 | `<EMAIL>` | ❌ |
| `SMTP_PASS` | 邮件服务器密码 | `your_app_password` | ❌ |

#### 🌐 应用配置

| 变量名 | 说明 | 示例值 | 必需 |
|--------|------|--------|------|
| `NEXT_PUBLIC_APP_URL` | 应用访问地址 | `http://localhost:3000` | ❌ |
| `NODE_ENV` | 运行环境 | `development` / `production` | ❌ |

### 数据库架构

项目使用 MySQL 8.0，包含以下核心数据表：

#### 👤 用户系统
- **users**: 用户基本信息（用户名、邮箱、密码哈希、角色、头像等）
- **email_verification**: 邮箱验证记录
- **password_reset**: 密码重置记录

#### 🤖 智能体系统
- **agents**: 智能体配置（名称、描述、系统提示词、模型参数、头像等）
- **agent_pricing_plans**: 智能体定价方案（按次/按时间收费）
- **agent_usage_logs**: 智能体使用记录和统计

#### 💳 订阅和付费
- **redemption_codes**: 兑换码管理（生成、使用、过期等）
- **redemption_code_usage**: 兑换码使用记录
- **user_agent_subscriptions**: 用户智能体订阅关系

#### ⚙️ 系统配置
- **ai_provider_configs**: AI提供商配置（OpenAI、Claude等）
- **user_settings**: 用户个人设置和偏好

#### 💬 对话系统
- **sessions**: 对话会话（标题、消息历史、智能体关联等）

## 📝 开发指南

### 可用脚本

#### 🚀 开发和构建
```bash
npm run dev          # 启动开发服务器 (端口3000)
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # ESLint 代码检查
npm run lint:fix     # 自动修复代码问题
npm run type-check   # TypeScript 类型检查
npm run clean        # 清理构建文件
npm run analyze      # 分析构建包大小
```

#### 🗄️ 数据库管理
```bash
npm run db:init      # 初始化数据库和表结构
npm run db:reset     # 重置数据库（删除并重建）
npm run setup-db     # 设置数据库（仅创建表）
```

#### ☁️ 部署相关
```bash
npm run deploy:vercel    # 部署到 Vercel
npm run deploy:preview   # 预览部署
npm run deploy:prod      # 生产环境部署
npm run setup-vercel     # 设置 Vercel 环境
```

### 添加新功能

1. 在 `src/app` 下创建新的页面路由
2. 在 `src/components` 下创建可复用组件
3. 在 `src/store` 下添加状态管理
4. 在 `src/app/api` 下添加 API 接口
5. 在 `src/types` 下定义 TypeScript 类型
6. 在 `src/services` 下添加业务服务

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 组件使用函数式组件和 React Hooks
- 状态管理使用 Zustand
- 样式使用 Ant Design 和 Tailwind CSS
- 提交前运行 `npm run lint` 和 `npm run type-check`

## 🚀 部署指南

### 🌐 Vercel 部署（推荐）

**优势**: 零配置、自动扩容、全球CDN、免费额度

1. **准备云数据库**
   ```bash
   # 推荐使用 Aiven MySQL (免费1个月)
   # 或 PlanetScale (免费5GB)
   ```

2. **一键部署**
   ```bash
   npm run deploy:vercel
   ```

3. **配置环境变量**
   - 在 Vercel 控制台设置数据库连接信息
   - 配置 JWT_SECRET 等敏感信息

详细部署指南请查看 [DEPLOYMENT.md](./DEPLOYMENT.md)

### 🐳 Docker 部署

1. **本地 Docker 部署**
   ```bash
   # 启动所有服务（包括MySQL）
   docker-compose up -d
   ```

2. **生产环境部署**
   ```bash
   # 使用部署脚本
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh production
   ```

### 📊 监控和维护

- **健康检查**: 访问 `/api/health` 检查系统状态
- **日志查看**: `docker-compose logs -f app`
- **数据备份**: 定期备份 MySQL 数据
- **性能监控**: 使用 Vercel Analytics 或自建监控

## 🔐 用户认证与权限

### 🔑 认证方式
- **邮箱密码登录**: 支持用户注册和登录
- **邮箱验证**: 可选的邮箱验证机制
- **密码重置**: 通过邮箱重置密码
- **JWT认证**: 无状态的令牌认证

### 👮 权限系统
- **角色管理**: admin（管理员）/ user（普通用户）
- **权限控制**: 基于角色的功能访问控制
  - `VIEW_SETTINGS`: 查看系统设置
  - `MANAGE_AGENTS`: 管理智能体
  - `MANAGE_PROMPTS`: 管理提示词
  - `MANAGE_USERS`: 管理用户

### 🛡️ 安全特性
- **密码加密**: bcrypt 哈希存储
- **JWT签名**: 防止令牌伪造
- **会话管理**: 自动过期和刷新
- **输入验证**: 防止 SQL 注入和 XSS
- **访问日志**: 记录用户操作历史

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔧 高级功能

### 🤖 AI 提供商管理
- **多提供商支持**: OpenAI、Claude、Gemini、DeepSeek
- **动态配置**: 实时切换和测试 AI 提供商
- **参数调优**: 自定义模型参数（温度、最大长度等）
- **连接测试**: 验证 API 密钥和连接状态
- **负载均衡**: 支持多个 API 密钥轮询使用

### 💳 订阅和计费系统
- **灵活定价**: 支持按次数和按时间两种计费模式
- **兑换码系统**: 批量生成和管理兑换码
- **试用机制**: 新用户免费试用次数
- **使用统计**: 详细的使用记录和分析
- **订阅管理**: 用户智能体订阅状态管理

### 📊 数据管理工具
- **数据库工具**: 内置数据库管理和维护工具
- **批量操作**: 支持批量数据导入和处理
- **数据同步**: 云数据库同步和备份工具
- **健康检查**: 系统状态监控和诊断

### 🎨 用户体验
- **响应式设计**: 完美适配桌面和移动设备
- **主题切换**: 支持明暗主题切换
- **国际化**: 支持多语言（中文为主）
- **快捷操作**: 键盘快捷键和批量操作
- **实时反馈**: 流式对话和实时状态更新

## � 项目状态

- ✅ **核心功能**: 智能对话、用户认证、权限管理
- ✅ **智能体系统**: 创建、管理、订阅、计费
- ✅ **兑换码系统**: 生成、使用、统计
- ✅ **AI提供商**: 多提供商支持和配置
- ✅ **部署方案**: Docker 和 Vercel 部署
- 🚧 **移动端优化**: 响应式设计持续改进
- 🚧 **API文档**: 完善的 API 文档
- 📋 **计划功能**: 批量生成、文件管理、数据分析

## 📞 支持与反馈

- **项目主页**: [微甜 AI Studio](https://story-ai.com)
- **技术支持**: 通过 Issues 提交问题和建议
- **部署指南**: 查看 [DEPLOYMENT.md](./DEPLOYMENT.md)
- **更新日志**: 关注 Git 提交记录

---

**微甜 AI Studio** - 企业级 AI 对话平台 🚀

> 让每个组织都能拥有自己的 AI 助手生态系统
